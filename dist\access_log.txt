[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:03] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:04] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:07:04] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:18] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:08:19] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: / | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/bootstrap.min.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /static/css/daterangepicker.css | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/jquery.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/moment.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/bootstrap.bundle.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/daterangepicker.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /static/js/echarts.min.js | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /static/fireworks.html | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /api/version | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /api/slaves | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:57] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:58] IP: 127.0.0.1 | Method: GET | Path: /api/leap8155/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:09:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:14:57] IP: 127.0.0.1 | Method: GET | Path: /api/data | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-04 16:14:58] IP: 127.0.0.1 | Method: GET | Path: /api/lp8155/top_issues | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
