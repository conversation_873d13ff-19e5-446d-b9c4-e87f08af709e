#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试exe文件的access_log功能
"""

import os
import sys
import time
import subprocess
import urllib.request
import urllib.error
import json

def test_complete_functionality():
    """完整测试exe文件功能"""
    print("🧪 完整功能测试 - TestDataAnalysisSystem")
    print("=" * 60)
    
    # 检查exe文件是否存在
    exe_path = os.path.join('dist', 'TestDataAnalysisSystem.exe')
    if not os.path.exists(exe_path):
        print("❌ exe文件不存在，请先运行打包脚本")
        return False
    
    print(f"✅ 找到exe文件: {exe_path}")
    
    # 检查版本文件是否在dist目录中
    version_files = ['version.json', 'version_info.json']
    for version_file in version_files:
        dist_version_path = os.path.join('dist', version_file)
        if os.path.exists(dist_version_path):
            print(f"✅ 版本文件已打包: {dist_version_path}")
            # 读取并显示版本信息
            try:
                with open(dist_version_path, 'r', encoding='utf-8') as f:
                    version_data = json.load(f)
                    if 'version' in version_data:
                        print(f"   版本: {version_data['version']}")
                    if 'build_time' in version_data:
                        print(f"   构建时间: {version_data['build_time']}")
            except Exception as e:
                print(f"   读取版本信息失败: {e}")
        else:
            print(f"❌ 版本文件未找到: {dist_version_path}")
    
    # 删除现有的access_log.txt（如果存在）
    access_log_path = 'access_log.txt'
    if os.path.exists(access_log_path):
        os.remove(access_log_path)
        print(f"🧹 已删除现有的access_log文件")
    
    # 启动exe文件
    print("🚀 启动exe文件...")
    try:
        # 启动exe进程
        process = subprocess.Popen(
            [exe_path],
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ exe进程已启动，PID: {process.pid}")
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        time.sleep(8)
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            print("❌ exe程序已退出")
            stdout, stderr = process.communicate()
            if stdout:
                print(f"输出: {stdout}")
            if stderr:
                print(f"错误: {stderr}")
            return False
        
        print("✅ exe程序正在运行")
        
        # 测试HTTP请求
        print("🌐 发送HTTP请求测试...")
        base_url = "http://127.0.0.1:5000"
        
        test_requests = [
            "/",
            "/api/access_log",
            "/static/index.html"
        ]
        
        successful_requests = 0
        for url in test_requests:
            try:
                full_url = f"{base_url}{url}"
                print(f"   请求: {url}")
                response = urllib.request.urlopen(full_url, timeout=10)
                status_code = response.getcode()
                print(f"   ✅ 状态码: {status_code}")
                successful_requests += 1
                time.sleep(1)  # 间隔1秒
            except urllib.error.URLError as e:
                print(f"   ❌ 请求失败: {e}")
            except Exception as e:
                print(f"   ❌ 请求出错: {e}")
        
        # 等待日志写入
        print("⏳ 等待日志写入...")
        time.sleep(3)
        
        # 检查access_log文件
        success = False
        if os.path.exists(access_log_path):
            print(f"✅ access_log文件已创建: {access_log_path}")
            
            # 显示文件大小
            file_size = os.path.getsize(access_log_path)
            print(f"📊 日志文件大小: {file_size} 字节")
            
            if file_size > 0:
                # 读取并显示日志内容
                with open(access_log_path, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                    log_lines = log_content.strip().split('\n')
                    print(f"📝 日志条目数: {len(log_lines)}")
                    print(f"📝 日志内容预览:")
                    print("-" * 50)
                    for i, line in enumerate(log_lines[:5], 1):  # 显示前5行
                        print(f"  {i}. {line}")
                    if len(log_lines) > 5:
                        print(f"  ... 还有 {len(log_lines) - 5} 行")
                    print("-" * 50)
                
                success = True
            else:
                print("⚠️  日志文件为空")
        else:
            print(f"❌ access_log文件未创建: {access_log_path}")
            
            # 检查是否在其他位置创建了
            dist_log_path = os.path.join('dist', 'access_log.txt')
            if os.path.exists(dist_log_path):
                print(f"⚠️  日志文件在dist目录中: {dist_log_path}")
        
        # 终止进程
        print("🛑 终止exe进程...")
        try:
            process.terminate()
            process.wait(timeout=5)
            print("✅ exe进程已正常终止")
        except subprocess.TimeoutExpired:
            process.kill()
            print("⚠️  强制终止exe进程")
        
        return success and successful_requests > 0
    
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def main():
    """主函数"""
    print("🧪 TestDataAnalysisSystem - 完整功能测试")
    print("=" * 70)
    
    success = test_complete_functionality()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 完整功能测试通过！")
        print("✅ exe文件可以正常启动和运行")
        print("✅ HTTP服务正常响应")
        print("✅ access_log文件在正确位置创建")
        print("✅ 版本信息文件已正确打包")
        print("📁 日志文件位置: 项目根目录/access_log.txt")
        print("📦 exe文件位置: dist/TestDataAnalysisSystem.exe")
        print("🔄 替换dist文件夹不会影响日志文件")
    else:
        print("❌ 功能测试失败，请检查配置")
    print("=" * 70)

if __name__ == '__main__':
    main()
