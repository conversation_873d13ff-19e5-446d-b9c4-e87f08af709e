#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试exe文件的完整功能
"""

import os
import sys
import time
import subprocess
import urllib.request
import urllib.error
import json

def test_final_functionality():
    """最终功能测试"""
    print("🧪 最终功能测试 - TestDataAnalysisSystem")
    print("=" * 60)
    
    # 检查exe文件是否存在
    exe_path = os.path.join('dist', 'TestDataAnalysisSystem.exe')
    if not os.path.exists(exe_path):
        print("❌ exe文件不存在，请先运行打包脚本")
        return False
    
    print(f"✅ 找到exe文件: {exe_path}")
    
    # 检查版本文件
    print("📋 检查版本文件:")
    version_files = ['version.json', 'version_info.json']
    for version_file in version_files:
        dist_version_path = os.path.join('dist', version_file)
        if os.path.exists(dist_version_path):
            print(f"   ✅ {version_file} 已打包")
        else:
            print(f"   ❌ {version_file} 未找到")
    
    # 删除现有的access_log.txt
    access_log_path = 'access_log.txt'
    if os.path.exists(access_log_path):
        os.remove(access_log_path)
        print(f"🧹 已删除现有的access_log文件")
    
    # 启动exe文件
    print("🚀 启动exe文件...")
    try:
        # 启动exe进程
        process = subprocess.Popen(
            [exe_path],
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ exe进程已启动，PID: {process.pid}")
        
        # 等待Flask完全启动（包括重启）
        print("⏳ 等待Flask服务完全启动...")
        time.sleep(15)  # 等待15秒让Flask完全启动
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            print("❌ exe程序已退出")
            stdout, stderr = process.communicate()
            if stdout:
                print(f"输出: {stdout}")
            if stderr:
                print(f"错误: {stderr}")
            return False
        
        print("✅ exe程序正在运行")
        
        # 测试HTTP请求
        print("🌐 发送HTTP请求测试...")
        base_url = "http://127.0.0.1:5000"
        
        test_requests = [
            ("/", "主页"),
            ("/api/access_log", "访问日志API"),
        ]
        
        successful_requests = 0
        for url, desc in test_requests:
            try:
                full_url = f"{base_url}{url}"
                print(f"   📡 请求 {desc}: {url}")
                
                # 创建请求
                req = urllib.request.Request(full_url)
                req.add_header('User-Agent', 'TestScript/1.0')
                
                response = urllib.request.urlopen(req, timeout=10)
                status_code = response.getcode()
                print(f"      ✅ 状态码: {status_code}")
                successful_requests += 1
                time.sleep(2)  # 间隔2秒
                
            except urllib.error.URLError as e:
                print(f"      ❌ 请求失败: {e}")
            except Exception as e:
                print(f"      ❌ 请求出错: {e}")
        
        # 等待日志写入
        print("⏳ 等待日志写入...")
        time.sleep(3)
        
        # 检查access_log文件
        success = False
        if os.path.exists(access_log_path):
            print(f"✅ access_log文件已创建: {access_log_path}")
            
            # 显示文件信息
            file_size = os.path.getsize(access_log_path)
            print(f"📊 日志文件大小: {file_size} 字节")
            
            if file_size > 0:
                # 读取并显示日志内容
                with open(access_log_path, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                    log_lines = log_content.strip().split('\n')
                    print(f"📝 日志条目数: {len(log_lines)}")
                    print(f"📝 日志内容:")
                    print("-" * 60)
                    for i, line in enumerate(log_lines, 1):
                        print(f"  {i}. {line}")
                    print("-" * 60)
                
                success = True
            else:
                print("⚠️  日志文件为空")
        else:
            print(f"❌ access_log文件未创建: {access_log_path}")
        
        # 终止进程
        print("🛑 终止exe进程...")
        try:
            process.terminate()
            process.wait(timeout=5)
            print("✅ exe进程已正常终止")
        except subprocess.TimeoutExpired:
            process.kill()
            print("⚠️  强制终止exe进程")
        
        return success and successful_requests > 0
    
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def show_summary():
    """显示总结信息"""
    print("\n" + "=" * 70)
    print("📋 功能实现总结:")
    print("=" * 70)
    
    print("✅ 1. 运行打包后的exe时，access_log路径放在dist文件夹外面")
    print("     - exe文件位置: dist/TestDataAnalysisSystem.exe")
    print("     - 日志文件位置: 项目根目录/access_log.txt")
    print("     - 替换dist文件夹不会影响access_log内容")
    
    print("\n✅ 2. 打包成exe命名为TestDataAnalysisSystem")
    print("     - exe文件名: TestDataAnalysisSystem.exe")
    
    print("\n✅ 3. 版本信息json文件打包到dist文件夹内")
    print("     - version.json: 包含版本号和构建信息")
    print("     - version_info.json: 包含详细版本信息")
    
    print("\n📁 目录结构:")
    print("项目根目录/")
    print("├── dist/")
    print("│   ├── TestDataAnalysisSystem.exe")
    print("│   ├── version.json")
    print("│   └── version_info.json")
    print("├── access_log.txt  ← 日志文件位置")
    print("└── 其他文件...")
    
    print("\n🚀 使用方法:")
    print("1. 将整个项目文件夹部署到目标环境")
    print("2. 运行 dist/TestDataAnalysisSystem.exe")
    print("3. access_log.txt 会在项目根目录自动创建")
    print("4. 可以安全地替换 dist 文件夹而不影响日志文件")

def main():
    """主函数"""
    print("🧪 TestDataAnalysisSystem - 最终功能测试")
    print("=" * 70)
    
    success = test_final_functionality()
    
    if success:
        print("\n🎉 所有功能测试通过！")
        print("✅ exe文件可以正常启动和运行")
        print("✅ HTTP服务正常响应")
        print("✅ access_log文件在正确位置创建并记录访问日志")
        print("✅ 版本信息文件已正确打包")
        show_summary()
    else:
        print("\n❌ 功能测试失败，请检查配置")
    
    print("=" * 70)

if __name__ == '__main__':
    main()
