#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI修改后的功能
"""

import time
import urllib.request
import urllib.error
import json

def test_version_api():
    """测试版本API返回的数据"""
    print("🔢 测试版本API...")
    try:
        req = urllib.request.Request("http://127.0.0.1:5000/api/version")
        response = urllib.request.urlopen(req, timeout=10)
        data = json.loads(response.read().decode('utf-8'))
        
        print(f"  ✅ 版本号: {data.get('version', 'N/A')}")
        print(f"  📅 发布日期: {data.get('release_date', 'N/A')}")
        print(f"  🔨 构建时间: {data.get('build_time', 'N/A')}")
        print(f"  🚀 服务启动: {data.get('service_start_time', 'N/A')}")
        print(f"  🔢 构建次数: {data.get('build_count', 'N/A')}")
        
        # 验证关键字段
        required_fields = ['version', 'build_time', 'release_date', 'service_start_time']
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            print(f"  ❌ 缺少字段: {missing_fields}")
            return False
        
        # 验证 release_date 是从 build_time 提取的
        build_time = data.get('build_time', '')
        release_date = data.get('release_date', '')
        
        if build_time and release_date:
            expected_date = build_time.split(' ')[0]
            if release_date == expected_date:
                print(f"  ✅ release_date 正确从 build_time 提取: {release_date}")
            else:
                print(f"  ❌ release_date 不匹配: 期望 {expected_date}, 实际 {release_date}")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 异常: {str(e)}")
        return False

def test_access_log_creation():
    """测试访问日志创建"""
    print("\n📝 测试访问日志创建...")
    try:
        # 发送几个请求
        urls = [
            "http://127.0.0.1:5000/",
            "http://127.0.0.1:5000/api/version",
        ]
        
        for url in urls:
            try:
                req = urllib.request.Request(url)
                req.add_header('User-Agent', 'TestScript-UI-Changes/1.0')
                response = urllib.request.urlopen(req, timeout=10)
                print(f"  📡 请求成功: {url} (状态码: {response.getcode()})")
                time.sleep(1)
            except Exception as e:
                print(f"  ❌ 请求失败: {url} - {e}")
        
        # 等待日志写入
        time.sleep(2)
        
        # 检查日志文件
        import os
        access_log_path = 'access_log.txt'
        if os.path.exists(access_log_path):
            print(f"  ✅ 访问日志文件已创建: {access_log_path}")
            
            with open(access_log_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                print(f"  📊 日志条目数: {len([line for line in lines if line.strip()])}")
                
                # 显示最后几条日志
                recent_lines = [line for line in lines if line.strip()][-3:]
                print(f"  📝 最近的日志条目:")
                for i, line in enumerate(recent_lines, 1):
                    print(f"    {i}. {line}")
            
            return True
        else:
            print(f"  ❌ 访问日志文件未找到: {access_log_path}")
            return False
            
    except Exception as e:
        print(f"  ❌ 异常: {str(e)}")
        return False

def test_ui_modifications():
    """测试UI修改"""
    print("\n🎨 测试UI修改...")
    
    # 检查HTML文件中的修改
    try:
        with open('static/index.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否移除了筛选控制区
        filter_section_removed = 'log-filter-section' not in content
        if filter_section_removed:
            print("  ✅ 访问日志筛选控制区已移除")
        else:
            print("  ❌ 访问日志筛选控制区仍然存在")
        
        # 检查是否修改了版本信息加载逻辑
        build_time_usage = 'data.build_time || data.service_start_time' in content
        if build_time_usage:
            print("  ✅ 版本信息已修改为使用 build_time")
        else:
            print("  ❌ 版本信息未正确修改")
        
        return filter_section_removed and build_time_usage
        
    except Exception as e:
        print(f"  ❌ 检查HTML文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🧪 测试UI修改后的功能")
    print("=" * 60)
    
    results = []
    
    # 测试版本API
    results.append(test_version_api())
    
    # 测试访问日志
    results.append(test_access_log_creation())
    
    # 测试UI修改
    results.append(test_ui_modifications())
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print("=" * 60)
    
    if all(results):
        print("🎉 所有测试通过！")
        print("✅ 版本API正确返回 build_time 和 release_date")
        print("✅ 访问日志功能正常工作")
        print("✅ UI修改已正确应用")
        print("\n🔧 修改内容:")
        print("1. ❌ 移除了访问日志弹窗中的筛选控制区域")
        print("2. 🔄 系统版本信息中的'服务启动'现在显示 version.json 的 build_time")
        print("3. 📅 release_date 自动从 build_time 中提取日期部分")
    else:
        print("❌ 部分测试失败，请检查配置")
        for i, result in enumerate(results, 1):
            status = "✅" if result else "❌"
            test_names = ["版本API测试", "访问日志测试", "UI修改测试"]
            print(f"  {status} {test_names[i-1]}")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
