#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试exe文件启动问题
"""

import os
import sys
import time
import subprocess

def debug_exe_startup():
    """调试exe文件启动"""
    print("🔍 调试exe文件启动")
    print("=" * 50)
    
    # 检查exe文件是否存在
    exe_path = os.path.join('dist', 'TestDataAnalysisSystem.exe')
    if not os.path.exists(exe_path):
        print("❌ exe文件不存在")
        return False
    
    print(f"✅ 找到exe文件: {exe_path}")
    
    # 启动exe文件并捕获输出
    print("🚀 启动exe文件并捕获输出...")
    try:
        # 启动exe进程，捕获所有输出
        process = subprocess.Popen(
            [exe_path],
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
            text=True,
            bufsize=1,  # 行缓冲
            universal_newlines=True
        )
        
        print(f"✅ exe进程已启动，PID: {process.pid}")
        
        # 实时读取输出
        print("📝 exe程序输出:")
        print("-" * 40)
        
        start_time = time.time()
        timeout = 15  # 15秒超时
        
        while True:
            # 检查进程是否结束
            if process.poll() is not None:
                print("⚠️  进程已结束")
                break
            
            # 检查超时
            if time.time() - start_time > timeout:
                print("⏰ 超时，终止进程")
                break
            
            # 读取输出
            try:
                line = process.stdout.readline()
                if line:
                    print(f"   {line.rstrip()}")
                else:
                    time.sleep(0.1)
            except Exception as e:
                print(f"   读取输出错误: {e}")
                break
        
        print("-" * 40)
        
        # 终止进程
        if process.poll() is None:
            print("🛑 终止进程...")
            process.terminate()
            try:
                process.wait(timeout=5)
                print("✅ 进程已终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️  强制终止进程")
        
        # 获取剩余输出
        try:
            remaining_output, _ = process.communicate(timeout=2)
            if remaining_output:
                print("📝 剩余输出:")
                print(remaining_output)
        except subprocess.TimeoutExpired:
            pass
        
        return True
    
    except Exception as e:
        print(f"❌ 启动exe文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 TestDataAnalysisSystem - exe启动调试")
    print("=" * 60)
    
    debug_exe_startup()
    
    print("\n" + "=" * 60)
    print("🔍 调试完成")
    print("💡 如果看到错误信息，请根据错误信息进行修复")
    print("=" * 60)

if __name__ == '__main__':
    main()
