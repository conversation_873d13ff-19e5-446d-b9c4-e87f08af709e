#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动构建脚本 - 打包时自动递增版本号
"""

import os
import sys
import subprocess
from datetime import datetime
from version_manager import increment_version, get_version_info

def build_exe():
    """构建EXE文件"""
    print("🚀 开始构建零跑测试数据分析系统...")
    print("=" * 60)
    
    # 1. 递增版本号
    print("📈 步骤1: 递增版本号...")
    try:
        new_version, build_time, build_count = increment_version()
        print(f"✅ 版本号已更新: v{new_version}")
        print(f"📅 构建时间: {build_time}")
        print(f"🔨 构建次数: {build_count}")
    except Exception as e:
        print(f"❌ 版本号递增失败: {str(e)}")
        return False
    
    # 2. 清理旧的构建文件
    print("\n🧹 步骤2: 清理旧构建文件...")
    try:
        if os.path.exists('dist'):
            import shutil
            shutil.rmtree('dist')
            print("✅ 已清理dist目录")
        if os.path.exists('build'):
            import shutil
            shutil.rmtree('build')
            print("✅ 已清理build目录")
    except Exception as e:
        print(f"⚠️ 清理警告: {str(e)}")
    
    # 3. 构建EXE
    print("\n🔨 步骤3: 构建EXE文件...")
    exe_name = f"LeapmotorTestAnalyzer-v{new_version}"
    
    build_command = [
        'pyinstaller',
        '--onefile',
        '--add-data', 'static;static',
        '--name', exe_name,
        'app.py'
    ]
    
    try:
        print(f"执行命令: {' '.join(build_command)}")
        result = subprocess.run(build_command, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ EXE构建成功!")
            
            # 检查生成的文件
            exe_path = f"dist/{exe_name}.exe"
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📦 生成文件: {exe_path}")
                print(f"📏 文件大小: {file_size:.1f} MB")
                
                # 创建版本信息文件
                create_version_info_file(new_version, build_time, build_count, exe_path)
                
                return True
            else:
                print(f"❌ 未找到生成的EXE文件: {exe_path}")
                return False
        else:
            print("❌ EXE构建失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建异常: {str(e)}")
        return False

def create_version_info_file(version, build_time, build_count, exe_path):
    """创建版本信息文件"""
    try:
        version_info = f"""零跑测试数据分析系统 - 版本信息
========================================

版本号: v{version}
构建时间: {build_time}
构建次数: {build_count}
文件路径: {exe_path}
文件大小: {os.path.getsize(exe_path) / (1024 * 1024):.1f} MB

更新内容:
- 修复AI分析站位过滤问题
- 移除"全部站位"选项，默认SOC测试
- 时间范围默认今天
- 版本号自动累加功能

使用说明:
1. 双击运行EXE文件
2. 浏览器自动打开 http://localhost:5000
3. 选择测试站位和时间范围进行数据分析
4. 使用AI分析功能获取智能报告

技术支持: 零跑汽车测试团队
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        info_file = f"dist/LeapmotorTestAnalyzer-v{version}-版本信息.txt"
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write(version_info)
        
        print(f"📄 版本信息文件已创建: {info_file}")
        
    except Exception as e:
        print(f"⚠️ 创建版本信息文件失败: {str(e)}")

def main():
    """主函数"""
    print("🎯 零跑测试数据分析系统 - 自动构建工具")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 显示当前版本信息
    current_info = get_version_info()
    print(f"📋 当前版本: {current_info['display_text']}")
    print(f"📅 上次构建: {current_info.get('build_time', '未知')}")
    print(f"🔨 构建次数: {current_info.get('build_count', 0)}")
    print()
    
    # 确认构建
    try:
        confirm = input("是否继续构建新版本? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 构建已取消")
            return
    except KeyboardInterrupt:
        print("\n❌ 构建已取消")
        return
    
    # 开始构建
    start_time = datetime.now()
    success = build_exe()
    end_time = datetime.now()
    
    # 构建结果
    duration = (end_time - start_time).total_seconds()
    print("\n" + "=" * 60)
    if success:
        print("🎉 构建完成!")
        print(f"⏱️ 耗时: {duration:.1f} 秒")
        print("💡 可以在dist目录中找到生成的EXE文件")
    else:
        print("💥 构建失败!")
        print(f"⏱️ 耗时: {duration:.1f} 秒")
        print("🔍 请检查错误信息并重试")
    
    print(f"🏁 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
