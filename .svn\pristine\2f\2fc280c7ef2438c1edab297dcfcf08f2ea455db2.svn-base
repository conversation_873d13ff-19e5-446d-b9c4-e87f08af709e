#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试exe文件的access_log路径功能
"""

import os
import sys
import time
import subprocess
import threading
import urllib.request
import urllib.error

def test_access_log_path():
    """测试access_log路径是否正确"""
    print("🧪 测试access_log路径功能")
    print("=" * 50)
    
    # 检查exe文件是否存在
    exe_path = os.path.join('dist', 'TestDataAnalysisSystem.exe')
    if not os.path.exists(exe_path):
        print("❌ exe文件不存在，请先运行打包脚本")
        return False
    
    print(f"✅ 找到exe文件: {exe_path}")
    
    # 删除现有的access_log.txt（如果存在）
    access_log_path = 'access_log.txt'
    if os.path.exists(access_log_path):
        os.remove(access_log_path)
        print(f"🧹 已删除现有的access_log文件")
    
    # 启动exe文件
    print("🚀 启动exe文件...")
    try:
        # 使用subprocess.Popen启动exe，不等待完成
        process = subprocess.Popen(
            [exe_path],
            cwd=os.getcwd(),  # 在当前目录运行
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        
        print(f"✅ exe进程已启动，PID: {process.pid}")
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        time.sleep(10)
        
        # 测试访问网站
        print("🌐 测试访问网站...")
        base_url = "http://127.0.0.1:5000"
        
        try:
            # 发送几个测试请求
            test_urls = [
                "/",
                "/api/access_log",
                "/static/index.html"
            ]
            
            for url in test_urls:
                try:
                    full_url = f"{base_url}{url}"
                    response = urllib.request.urlopen(full_url, timeout=5)
                    print(f"✅ 访问 {url} - 状态码: {response.getcode()}")
                except urllib.error.URLError as e:
                    print(f"⚠️  访问 {url} 失败: {e}")
                except Exception as e:
                    print(f"⚠️  访问 {url} 出错: {e}")
            
            # 等待一下让日志写入
            time.sleep(2)
            
            # 检查access_log文件是否在正确位置创建
            if os.path.exists(access_log_path):
                print(f"✅ access_log文件已在正确位置创建: {access_log_path}")
                
                # 读取并显示日志内容
                with open(access_log_path, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                    print(f"📝 日志内容预览:")
                    print("-" * 30)
                    print(log_content[:500] + "..." if len(log_content) > 500 else log_content)
                    print("-" * 30)
                
                return True
            else:
                print(f"❌ access_log文件未在预期位置创建: {access_log_path}")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程出错: {e}")
            return False
        
        finally:
            # 终止exe进程
            print("🛑 终止exe进程...")
            try:
                process.terminate()
                process.wait(timeout=5)
                print("✅ exe进程已终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️  强制终止exe进程")
            except Exception as e:
                print(f"⚠️  终止进程时出错: {e}")
    
    except Exception as e:
        print(f"❌ 启动exe文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 TestDataAnalysisSystem - Access Log 路径测试")
    print("=" * 60)
    
    success = test_access_log_path()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试通过！access_log路径功能正常")
        print("📁 日志文件位置: 项目根目录/access_log.txt")
        print("✅ 替换dist文件夹不会影响日志文件")
    else:
        print("❌ 测试失败，请检查配置")
    print("=" * 60)

if __name__ == '__main__':
    main()
