#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试access_log路径逻辑
"""

import os
import sys

# 模拟exe环境的路径逻辑
def get_access_log_path_simulation():
    """模拟exe环境下的路径逻辑"""
    # 模拟exe文件路径
    exe_path = os.path.join(os.getcwd(), 'dist', 'TestDataAnalysisSystem.exe')
    
    # 模拟sys.executable在exe环境下的值
    exe_dir = os.path.dirname(exe_path)  # dist目录
    parent_dir = os.path.dirname(exe_dir)  # 项目根目录
    log_path = os.path.join(parent_dir, 'access_log.txt')
    
    return log_path, exe_path, exe_dir, parent_dir

def test_path_logic():
    """测试路径逻辑"""
    print("🧪 测试access_log路径逻辑")
    print("=" * 50)
    
    # 当前目录信息
    current_dir = os.getcwd()
    print(f"📁 当前工作目录: {current_dir}")
    
    # 模拟exe环境
    log_path, exe_path, exe_dir, parent_dir = get_access_log_path_simulation()
    
    print(f"📁 模拟exe文件路径: {exe_path}")
    print(f"📁 exe所在目录(dist): {exe_dir}")
    print(f"📁 父目录(项目根): {parent_dir}")
    print(f"📁 预期日志路径: {log_path}")
    
    # 验证路径
    print("\n🔍 路径验证:")
    
    # 检查exe文件是否存在
    if os.path.exists(exe_path):
        print(f"✅ exe文件存在: {exe_path}")
    else:
        print(f"❌ exe文件不存在: {exe_path}")
    
    # 检查目录结构
    if os.path.exists(exe_dir):
        print(f"✅ dist目录存在: {exe_dir}")
    else:
        print(f"❌ dist目录不存在: {exe_dir}")
    
    if os.path.exists(parent_dir):
        print(f"✅ 项目根目录存在: {parent_dir}")
    else:
        print(f"❌ 项目根目录不存在: {parent_dir}")
    
    # 检查日志路径是否在正确位置
    expected_log_path = os.path.join(current_dir, 'access_log.txt')
    if log_path == expected_log_path:
        print(f"✅ 日志路径正确: {log_path}")
    else:
        print(f"❌ 日志路径不正确:")
        print(f"   预期: {expected_log_path}")
        print(f"   实际: {log_path}")
    
    return log_path == expected_log_path

def main():
    """主函数"""
    print("🧪 TestDataAnalysisSystem - 路径逻辑测试")
    print("=" * 60)
    
    success = test_path_logic()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 路径逻辑测试通过！")
        print("✅ access_log将在正确位置创建")
    else:
        print("❌ 路径逻辑测试失败")
    print("=" * 60)

if __name__ == '__main__':
    main()
