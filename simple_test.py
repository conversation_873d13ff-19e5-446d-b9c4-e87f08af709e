#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试exe文件的access_log路径功能
"""

import os
import sys
import time
import subprocess

def test_exe_and_log():
    """测试exe文件和日志路径"""
    print("🧪 简单测试 - TestDataAnalysisSystem")
    print("=" * 50)
    
    # 检查exe文件是否存在
    exe_path = os.path.join('dist', 'TestDataAnalysisSystem.exe')
    if not os.path.exists(exe_path):
        print("❌ exe文件不存在，请先运行打包脚本")
        return False
    
    print(f"✅ 找到exe文件: {exe_path}")
    
    # 检查版本文件是否在dist目录中
    version_files = ['version.json', 'version_info.json']
    for version_file in version_files:
        dist_version_path = os.path.join('dist', version_file)
        if os.path.exists(dist_version_path):
            print(f"✅ 版本文件已打包: {dist_version_path}")
        else:
            print(f"❌ 版本文件未找到: {dist_version_path}")
    
    # 删除现有的access_log.txt（如果存在）
    access_log_path = 'access_log.txt'
    if os.path.exists(access_log_path):
        os.remove(access_log_path)
        print(f"🧹 已删除现有的access_log文件")
    
    # 检查当前目录结构
    print(f"📁 当前工作目录: {os.getcwd()}")
    print(f"📁 exe文件路径: {os.path.abspath(exe_path)}")
    print(f"📁 预期日志路径: {os.path.abspath(access_log_path)}")
    
    # 启动exe文件（短时间测试）
    print("🚀 启动exe文件进行短时间测试...")
    try:
        # 启动exe进程
        process = subprocess.Popen(
            [exe_path],
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ exe进程已启动，PID: {process.pid}")
        
        # 等待5秒让程序初始化
        print("⏳ 等待程序初始化...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ exe程序正在运行")
        else:
            print("❌ exe程序已退出")
            stdout, stderr = process.communicate()
            if stdout:
                print(f"输出: {stdout}")
            if stderr:
                print(f"错误: {stderr}")
            return False
        
        # 终止进程
        print("🛑 终止exe进程...")
        process.terminate()
        
        # 等待进程结束
        try:
            process.wait(timeout=5)
            print("✅ exe进程已正常终止")
        except subprocess.TimeoutExpired:
            process.kill()
            print("⚠️  强制终止exe进程")
        
        # 检查是否创建了access_log文件
        if os.path.exists(access_log_path):
            print(f"✅ access_log文件已在正确位置创建: {access_log_path}")
            
            # 显示文件大小
            file_size = os.path.getsize(access_log_path)
            print(f"📊 日志文件大小: {file_size} 字节")
            
            if file_size > 0:
                # 读取并显示日志内容
                with open(access_log_path, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                    print(f"📝 日志内容:")
                    print("-" * 30)
                    print(log_content)
                    print("-" * 30)
            
            return True
        else:
            print(f"❌ access_log文件未在预期位置创建: {access_log_path}")
            
            # 检查是否在dist目录中创建了
            dist_log_path = os.path.join('dist', 'access_log.txt')
            if os.path.exists(dist_log_path):
                print(f"⚠️  日志文件在dist目录中: {dist_log_path}")
                print("⚠️  这表明路径配置可能有问题")
            
            return False
    
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def main():
    """主函数"""
    print("🧪 TestDataAnalysisSystem - 简单功能测试")
    print("=" * 60)
    
    success = test_exe_and_log()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试通过！")
        print("✅ exe文件可以正常启动")
        print("✅ access_log文件在正确位置创建")
        print("✅ 版本信息文件已打包到dist目录")
        print("📁 日志文件位置: 项目根目录/access_log.txt")
        print("📦 exe文件位置: dist/TestDataAnalysisSystem.exe")
    else:
        print("❌ 测试失败，请检查配置")
    print("=" * 60)

if __name__ == '__main__':
    main()
